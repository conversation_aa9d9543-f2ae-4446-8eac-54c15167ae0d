# Tóm tắt Chức năng Chính - Dự án Keypoint Detection

## 🎯 Mục đích dự án
Phát hiện và định vị 17 điểm khớp (keypoints) trên cơ thể người trong ảnh, hỗ trợ đa người trong cùng một ảnh.

## 🏗️ Kiến trúc tổng thể

### 1. **MultiPersonKeypointModel** - Model chính
- **Chức năng**: <PERSON><PERSON><PERSON> hợp tất cả components, end-to-end training
- **Input**: Images [B, C, H, W]
- **Output**: Keypoints coordinates + visibility scores
- **Tại sao**: Modular design, dễ maintain và extend

### 2. **MobileNetV3Wrapper** - Backbone
- **Chức năng**: Feature extraction từ images
- **Công nghệ**: MobileNetV3-Small với channel attention
- **Tại sao**: T<PERSON>i <PERSON>u cho mobile, balance accuracy/speed

### 3. **PERSON_HEAD** - Person Detection
- **Chức năng**: Detect bounding boxes của người
- **Công nghệ**: Object detection + NMS
- **Tại sao**: Multi-person support, loại bỏ duplicate

### 4. **HeatmapHead** - Spatial Representation
- **Chức năng**: Generate heatmaps cho keypoints
- **Công nghệ**: Deconvolutional layers + Gaussian targets
- **Tại sao**: Spatial awareness, differentiable training

### 5. **KEYPOINT_HEAD** - Direct Regression
- **Chức năng**: Predict coordinates trực tiếp
- **Công nghệ**: Regression + visibility classification
- **Tại sao**: Fine-grained localization, fast inference

## 📊 Data Processing Pipeline

### 1. **OptimizedKeypointsDataset**
- **Chức năng**: Load và preprocess data efficiently
- **Công nghệ**: LRU caching, structured annotations
- **Tại sao**: Fast I/O, memory optimization

### 2. **KeypointAugmentation**
- **Chức năng**: Data augmentation cho keypoints
- **Công nghệ**: Geometric transformations
- **Tại sao**: Improve generalization, prevent overfitting

### 3. **AdaptiveBatchSampler**
- **Chức năng**: Dynamic batching theo số người
- **Công nghệ**: Custom PyTorch sampler
- **Tại sao**: Memory optimization, handle variable persons

## 🎯 Loss Functions

### 1. **KeypointLoss** - Advanced Loss System
- **Chức năng**: Dynamic loss balancing
- **Components**:
  - Keypoint Loss: MSE coordinates
  - Visibility Loss: Binary classification
  - Heatmap Loss: Spatial representation
- **Tại sao**: Address training instability, better convergence

### 2. **Dynamic Balancing**
- **Chức năng**: Adaptive loss weights
- **Công nghệ**: Learnable parameters hoặc heuristic
- **Tại sao**: Balance different loss components

## 🚀 Training System

### 1. **Trainer Class**
- **Chức năng**: Main training loop coordination
- **Features**:
  - Mixed precision training (AMP)
  - Gradient clipping
  - Learning rate scheduling
  - Early stopping
  - Checkpointing
- **Tại sao**: Robust training process, reproducibility

### 2. **TrainingHistory**
- **Chức năng**: Track metrics và progress
- **Metrics**: PCK, ADE, loss components
- **Tại sao**: Monitor training, debugging, analysis

## ⚙️ Configuration System

### 1. **YAML Configuration**
- **Files**: `default_config.yaml`
- **Sections**: device, model, training, augmentation
- **Tại sao**: Human-readable, easy experimentation

### 2. **Configuration Classes**
- **ModelConfig**: Architecture parameters
- **TrainingConfig**: Training hyperparameters
- **DeviceConfig**: Hardware settings
- **Tại sao**: Type safety, validation, IDE support

## 🛠️ Utilities

### 1. **Device Manager**
- **Chức năng**: Hardware management (CPU/GPU)
- **Features**: Auto-detection, memory optimization
- **Tại sao**: Flexible deployment, error handling

### 2. **Logger System**
- **Chức năng**: Comprehensive logging
- **Features**: File + console output, different levels
- **Tại sao**: Debugging, monitoring, production

### 3. **Metrics Calculator**
- **Chức năng**: Evaluation metrics computation
- **Metrics**: PCK@multiple thresholds, ADE
- **Tại sao**: Objective performance assessment

## 📊 Evaluation & Metrics

### 1. **PCK (Percentage of Correct Keypoints)**
- **Công thức**: (correct_keypoints / total_keypoints) × 100
- **Thresholds**: 0.002, 0.05, 0.2 (normalized)
- **Tại sao**: Standard metric cho keypoint accuracy

### 2. **ADE (Average Distance Error)**
- **Công thức**: Mean Euclidean distance
- **Unit**: Pixels
- **Tại sao**: Intuitive error measurement

## 🎨 Visualization Tools

### 1. **backbone_vis.py**
- **Chức năng**: Model analysis và debugging
- **Features**: Feature visualization, attention maps
- **Tại sao**: Understanding model behavior

## 📦 Scripts & Deployment

### 1. **train.py**
- **Chức năng**: Training script với CLI interface
- **Features**: Config loading, argument parsing
- **Tại sao**: Easy to use, reproducible experiments

### 2. **predict.py**
- **Chức năng**: Inference script
- **Features**: Single image processing
- **Tại sao**: Quick testing, demo purposes

## 🔧 Dependencies & Tools

### Core Libraries
- **PyTorch**: Deep learning framework
- **torchvision**: Computer vision operations
- **numpy**: Numerical computing
- **PIL**: Image processing
- **opencv-python**: Advanced CV operations

### Development Tools
- **pyyaml**: Configuration parsing
- **tqdm**: Progress monitoring
- **pandas**: Data analysis
- **openpyxl**: Documentation export

## 💡 Tại sao thiết kế như vậy?

### 1. **Modular Architecture**
- Dễ maintain và extend
- Reusable components
- Clear separation of concerns

### 2. **Configuration-driven**
- Flexible experimentation
- Reproducible results
- Easy parameter tuning

### 3. **Production-ready**
- Robust error handling
- Comprehensive logging
- Hardware optimization

### 4. **Research-friendly**
- Advanced loss functions
- Comprehensive metrics
- Visualization tools

---

**Tổng kết**: Đây là một hệ thống AI hoàn chỉnh cho keypoint detection với thiết kế modular, performance cao và dễ sử dụng.
