paths:
  default_config: 'F:/AI_MeTeam/project_root/keypoint-detection/configs/default_config.yaml'
  data_dir: 'F:/AI_MeTeam/dataset'
  output_dir: 'F:/AI_MeTeam/project_root/keypoint-detection/outputs'

# Device configuration - centralized device management
device:
  # Device type: 'auto', 'cuda', 'cpu', or specific device like 'cuda:0'
  type: 'auto'  # auto will use cuda if available, otherwise cpu
  # Force CPU even if CUDA is available (useful for debugging)
  force_cpu: false
  # Enable mixed precision training (only works with CUDA)
  mixed_precision: true
  # Pin memory for faster data transfer (only works with CUDA)
  pin_memory: true

model:
  backbone:
    width_mult: 1.5
    in_channels: 1
    out_channels: 128
    input_size: 224
    convert_to_grayscale: true

  person_head:
    in_channels: 128
    num_classes: 1
    conf_threshold: 0.3
    nms_iou_threshold: 0.3
    anchor_sizes: [32, 64, 128]

  keypoint_head:
    in_channels: 128
    num_keypoints: 17
    height: 56
    width: 56
    fine_branch_channels: 64
    regression_channels: 32
    visibility_channels: 32
    dropout_rate: 0.2

  heatmap_head:
    in_channels: 64
    hidden_channels: 64
    num_keypoints: 17
    heatmap_size: [56, 56]  # Match dataloader heatmap size
    dropout_rate: 0.1
    use_attention: true
    num_deconv_layers: 2
    deconv_kernel_sizes: [4, 4]
    deconv_channels: [256, 256]

training:
  num_epochs: 30  # Increase for better training
  batch_size: 128   # Reduce for stability
  num_workers: 0  # Set to 0 to avoid multiprocessing issues with DeviceManager
  save_interval: 10
  validation_interval: 10
  early_stopping_patience: 10
  gradient_clip_norm: 1.0
  pck_thresholds: [0.002, 0.05, 0.2]

  # Learning rate scheduler configuration
  lr_scheduler:
    factor: 0.1        # Reduce LR by factor of 10
    patience: 3        # Wait 3 epochs without improvement
    min_lr: 1e-6       # Minimum learning rate
    mode: 'min'        # Monitor decreasing metric (loss)
    threshold: 0.0001  # Minimum improvement threshold
    metric: 'loss'     # Which metric to monitor ('loss' or 'pck_0.2')

  optimizer:
    name: "adam" # hoặc "sgd"
    learning_rate: 0.01
    weight_decay: 0.001
    momentum: 0.9 # cho SGD
    beta1: 0.9 # cho Adam
    beta2: 0.999 # cho Adam

  augmentation:
    enabled: false   # Enable augmentations for better generalization
    prob: 0.5
    flip:
      enabled: true
      horizontal: true
    rotate:
      enabled: true
      max_angle: 15.0
    scale:
      enabled: true
      range: [0.8, 1.2]

  loss:
    keypoint_loss_weight: 20.0  # Increased for better keypoint learning
    visibility_loss_weight: 8.0  # Increased for better visibility learning
    focal_gamma: 2.5
    focal_alpha: 0.25
    learnable_focal_params: false
    label_smoothing: 0.05
    # New weighted loss parameters
    weighted_loss:
      enabled: true
      keypoint_weight: 15.0  # Higher weight for keypoint regions
      background_weight: 1.0  # Lower weight for background
      threshold: 0.1  # Threshold to distinguish keypoint vs background


